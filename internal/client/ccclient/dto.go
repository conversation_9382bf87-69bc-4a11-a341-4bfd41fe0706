package ccclient

type (
	GetDeclarationResultRequest struct {
		TicketNumber string `json:"ticket_number"`
		BizType      int    `json:"biz_type"`
		DeclareType  int    `json:"declare_type"`
	}

	GetDeclarationResultResponse struct {
		Retcode int                      `json:"retcode"`
		Message string                   `json:"message"`
		Data    GetDeclarationResultData `json:"data,omitempty"`
	}

	GetDeclarationResultData struct {
		List []GetDeclarationResultDataItem `json:"list"`
	}

	GetDeclarationResultDataItem struct {
		CustomsAgentId string `json:"customs_agent_id"`
		TicketNumber   string `json:"ticket_number"`
		DeclareType    int    `json:"declare_type"`
	}

	// ListCustomsAgents API
	ListCustomsAgentsRequest struct {
		Region      string `json:"region"`
		DeclareType int    `json:"declare_type"`
	}

	ListCustomsAgentsResponse struct {
		Retcode int                   `json:"retcode"`
		Message string                `json:"message"`
		Data    ListCustomsAgentsData `json:"data,omitempty"`
	}

	ListCustomsAgentsData struct {
		List []*CustomsAgent `json:"list"`
	}

	CustomsAgent struct {
		Id   string `json:"id"`
		Name string `json:"name"`
	}
)
